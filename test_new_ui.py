#!/usr/bin/env python3
"""
测试新的界面设计
"""

import tkinter as tk
from tkinter import messagebox

def test_main_ui():
    """测试主界面"""
    try:
        from 主程序 import create_gui, setup_global_exception_handler
        print("✅ 主界面模块导入成功")
        
        # 可以在这里添加更多测试
        return True
    except Exception as e:
        print(f"❌ 主界面测试失败: {e}")
        return False

def test_email_client_ui():
    """测试邮箱客户端界面"""
    try:
        from 邮箱客户端界面 import show_email_client, EmailClientWindow
        print("✅ 邮箱客户端界面模块导入成功")
        
        # 测试类实例化
        root = tk.Tk()
        root.withdraw()
        client = EmailClientWindow(root)
        print("✅ 邮箱客户端窗口类实例化成功")
        root.destroy()
        
        return True
    except Exception as e:
        print(f"❌ 邮箱客户端界面测试失败: {e}")
        return False

def test_ui_components():
    """测试界面组件"""
    try:
        from 界面组件 import (
            create_auto_login_frame,
            create_email_credentials_frame,
            create_account_copy_frame,
            create_system_tools_frame,
            create_browser_settings_frame
        )
        print("✅ 界面组件模块导入成功")
        
        # 测试组件创建
        root = tk.Tk()
        root.withdraw()
        
        frame = tk.Frame(root)
        
        # 测试各个组件
        auto_frame, auto_btn = create_auto_login_frame(frame)
        print("✅ 自动登录框架创建成功")
        
        cred_frame, prefix_entry, pwd_entry, save_btn = create_email_credentials_frame(frame, "test", "test")
        print("✅ 邮箱凭据框架创建成功")
        
        acc_frame, email_entry, copy_btn = create_account_copy_frame(frame)
        print("✅ 账户信息框架创建成功")
        
        sys_frame, reset_btn, email_btn = create_system_tools_frame(frame)
        print("✅ 系统工具框架创建成功")
        
        browser_frame, path_entry, browse_btn, save_btn2 = create_browser_settings_frame(frame, "")
        print("✅ 浏览器设置框架创建成功")
        
        root.destroy()
        return True
    except Exception as e:
        print(f"❌ 界面组件测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 开始新界面设计测试...\n")
    
    tests = [
        ("主界面", test_main_ui),
        ("邮箱客户端界面", test_email_client_ui),
        ("界面组件", test_ui_components)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"📋 测试 {test_name}:")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过\n")
            else:
                print(f"❌ {test_name} 测试失败\n")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}\n")
    
    print("="*50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！新界面设计可以正常使用。")
        return True
    else:
        print("❌ 部分测试失败，请检查相关问题。")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
