import tkinter as tk
from tkinter import ttk
from 工具模块 import EMOJI

def create_auto_login_frame(parent):
    frame = ttk.LabelFrame(parent, text="一键登录工具", padding="10")
    button = ttk.Button(frame, text=f"{EMOJI['LOGIN']} 一键登录 (当前页面)")
    button.pack(fill=tk.X, expand=True, padx=2)
    return frame, button

def create_account_copy_frame(parent):
    frame = ttk.LabelFrame(parent, text="账号信息 (方便复制)", padding="10")
    
    ttk.Label(frame, text="邮箱:").grid(row=0, column=0, sticky=tk.W)
    entry = ttk.Entry(frame, width=30, state='readonly')
    entry.grid(row=0, column=1, sticky="we", padx=5)
    
    button = ttk.Button(frame, text="复制")
    button.grid(row=0, column=2, sticky="e")

    frame.columnconfigure(1, weight=1)
    return frame, entry, button

def create_email_credentials_frame(parent, prefix, password):
    """创建邮箱凭据设置框架，包括前缀和密码"""
    frame = ttk.LabelFrame(parent, text="邮箱凭据设置", padding="10")
    
    # Prefix
    ttk.Label(frame, text="邮箱前缀:").grid(row=0, column=0, sticky=tk.W, pady=2)
    prefix_entry = ttk.Entry(frame, width=30)
    prefix_entry.insert(0, prefix)
    prefix_entry.grid(row=0, column=1, columnspan=2, sticky="we", padx=5)
    
    # Password
    ttk.Label(frame, text="邮箱密码:").grid(row=1, column=0, sticky=tk.W, pady=2)
    password_entry = ttk.Entry(frame, width=30)
    password_entry.insert(0, password)
    password_entry.grid(row=1, column=1, columnspan=2, sticky="we", padx=5)

    # Save Button
    save_button = ttk.Button(frame, text="保存凭据")
    save_button.grid(row=2, column=2, sticky="e", pady=(10, 0))
    
    frame.columnconfigure(1, weight=1)
    return frame, prefix_entry, password_entry, save_button

def create_system_tools_frame(parent):
    frame = ttk.LabelFrame(parent, text="系统工具", padding="10")
    reset_button = ttk.Button(frame, text="一键重置环境")
    reset_button.pack(fill=tk.X, expand=True, padx=2, pady=2)
    email_client_button = ttk.Button(frame, text="打开邮箱客户端")
    email_client_button.pack(fill=tk.X, expand=True, padx=2, pady=2)
    return frame, reset_button, email_client_button

def create_browser_settings_frame(parent, browser_path):
    """创建浏览器路径设置框架"""
    frame = ttk.LabelFrame(parent, text="浏览器设置", padding="10")

    # Path Entry
    ttk.Label(frame, text="Chrome路径:").grid(row=0, column=0, sticky=tk.W, pady=2)
    path_entry = ttk.Entry(frame, width=30)
    path_entry.insert(0, browser_path)
    path_entry.grid(row=0, column=1, sticky="we", padx=5)

    # Browse Button
    browse_button = ttk.Button(frame, text="浏览...")
    browse_button.grid(row=0, column=2, sticky="e", padx=(0, 5))

    # Save Button
    save_button = ttk.Button(frame, text="保存路径")
    save_button.grid(row=1, column=2, sticky="e", pady=(10, 0))
    
    frame.columnconfigure(1, weight=1)
    return frame, path_entry, browse_button, save_button 