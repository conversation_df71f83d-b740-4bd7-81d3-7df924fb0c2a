#!/usr/bin/env python3
"""
界面美化演示脚本 - 展示美化后的界面效果
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time

def show_ui_demo():
    """显示界面美化演示"""
    root = tk.Tk()
    root.title("🎨 界面美化演示")
    root.geometry("800x600")
    root.configure(bg='#f8fafc')
    
    # 居中窗口
    root.update_idletasks()
    width = root.winfo_width()
    height = root.winfo_height()
    x = (root.winfo_screenwidth() // 2) - (width // 2)
    y = (root.winfo_screenheight() // 2) - (height // 2)
    root.geometry(f"{width}x{height}+{x}+{y}")
    
    main_frame = ttk.Frame(root, padding="20")
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    # 标题
    title_label = ttk.Label(main_frame, text="🎨 续杯工具界面美化展示", 
                           font=("Microsoft YaHei UI", 18, "bold"))
    title_label.pack(pady=(0, 20))
    
    # 美化特性列表
    features_frame = ttk.LabelFrame(main_frame, text="✨ 美化特性", padding="20")
    features_frame.pack(fill=tk.X, pady=(0, 20))
    
    features = [
        "🎨 现代化的颜色主题和配色方案",
        "🔤 优化的字体搭配 (Microsoft YaHei UI + Consolas)",
        "📐 改进的布局和间距设计",
        "🎯 清晰的视觉层次和信息组织",
        "💫 丰富的表情符号和图标",
        "📱 响应式布局设计",
        "🔍 状态指示器和实时反馈",
        "🎪 专业的界面组件样式"
    ]
    
    for i, feature in enumerate(features):
        feature_label = ttk.Label(features_frame, text=feature, 
                                 font=("Microsoft YaHei UI", 10))
        feature_label.pack(anchor="w", pady=2)
    
    # 按钮区域
    button_frame = ttk.Frame(main_frame)
    button_frame.pack(fill=tk.X, pady=20)
    
    def show_main_ui():
        """显示主界面"""
        try:
            from 主程序 import create_gui, setup_global_exception_handler
            setup_global_exception_handler()
            
            # 在新线程中启动主界面
            def start_main():
                create_gui()
            
            threading.Thread(target=start_main, daemon=True).start()
            messagebox.showinfo("启动成功", "主界面已在新窗口中打开！")
        except Exception as e:
            messagebox.showerror("启动失败", f"无法启动主界面: {e}")
    
    def show_email_client():
        """显示邮箱客户端"""
        try:
            from 邮箱客户端界面 import show_email_client
            show_email_client(root)
            messagebox.showinfo("启动成功", "邮箱客户端已打开！")
        except Exception as e:
            messagebox.showerror("启动失败", f"无法启动邮箱客户端: {e}")
    
    # 美化的按钮
    main_btn = ttk.Button(button_frame, text="🔑 查看主界面", command=show_main_ui)
    main_btn.pack(side=tk.LEFT, padx=(0, 10), ipady=8, ipadx=20)
    
    email_btn = ttk.Button(button_frame, text="📧 查看邮箱客户端", command=show_email_client)
    email_btn.pack(side=tk.LEFT, padx=(0, 10), ipady=8, ipadx=20)
    
    close_btn = ttk.Button(button_frame, text="❌ 关闭演示", command=root.destroy)
    close_btn.pack(side=tk.RIGHT, ipady=8, ipadx=20)
    
    # 说明文字
    info_frame = ttk.LabelFrame(main_frame, text="📖 使用说明", padding="15")
    info_frame.pack(fill=tk.X)
    
    info_text = """
美化后的界面特点：

1. 🎨 视觉设计：采用现代化的扁平设计风格，清爽简洁
2. 🔤 字体优化：使用Microsoft YaHei UI作为主字体，Consolas用于代码显示
3. 🎯 布局改进：重新设计了组件布局，提高了可用性
4. 💫 交互体验：添加了状态指示器和实时反馈
5. 📱 响应式：支持窗口缩放，适应不同屏幕尺寸

点击上方按钮可以查看美化后的界面效果！
    """
    
    info_label = ttk.Label(info_frame, text=info_text.strip(), 
                          font=("Microsoft YaHei UI", 9), justify=tk.LEFT)
    info_label.pack(anchor="w")
    
    root.mainloop()

if __name__ == "__main__":
    show_ui_demo()
