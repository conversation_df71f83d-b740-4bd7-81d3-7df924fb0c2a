#!/usr/bin/env python3
"""
测试所有模块导入是否正常
"""

def test_imports():
    """测试所有必要的模块导入"""
    errors = []
    
    # 测试标准库
    try:
        import sys, os, tkinter, threading, logging, multiprocessing
        import sqlite3, configparser, traceback, time, random, string
        import poplib, email, re, subprocess, platform, json, uuid
        print("✅ 标准库导入成功")
    except ImportError as e:
        errors.append(f"❌ 标准库导入失败: {e}")
    
    # 测试第三方库
    try:
        import colorama
        print("✅ colorama 导入成功")
    except ImportError as e:
        errors.append(f"❌ colorama 导入失败: {e}")
    
    try:
        import pywinauto
        print("✅ pywinauto 导入成功")
    except ImportError as e:
        errors.append(f"❌ pywinauto 导入失败: {e}")
    
    try:
        from DrissionPage import ChromiumPage, ChromiumOptions
        print("✅ DrissionPage 导入成功")
    except ImportError as e:
        errors.append(f"❌ DrissionPage 导入失败: {e}")
    
    try:
        import pyperclip
        print("✅ pyperclip 导入成功")
    except ImportError as e:
        errors.append(f"❌ pyperclip 导入失败: {e}")
    
    try:
        from bs4 import BeautifulSoup
        print("✅ BeautifulSoup 导入成功")
    except ImportError as e:
        errors.append(f"❌ BeautifulSoup 导入失败: {e}")
    
    try:
        import PyInstaller
        print("✅ PyInstaller 导入成功")
    except ImportError as e:
        errors.append(f"❌ PyInstaller 导入失败: {e}")
    
    # 测试项目模块
    try:
        from 工具模块 import EMOJI, get_default_browser_path
        print("✅ 工具模块 导入成功")
    except ImportError as e:
        errors.append(f"❌ 工具模块 导入失败: {e}")
    
    try:
        from 配置管理 import get_config, save_config
        print("✅ 配置管理 导入成功")
    except ImportError as e:
        errors.append(f"❌ 配置管理 导入失败: {e}")
    
    try:
        from 应用管理 import run_full_reset_flow, launch_email_client_process
        print("✅ 应用管理 导入成功")
    except ImportError as e:
        errors.append(f"❌ 应用管理 导入失败: {e}")
    
    try:
        from 网页操作 import generate_random_email, run_auto_login_flow
        print("✅ 网页操作 导入成功")
    except ImportError as e:
        errors.append(f"❌ 网页操作 导入失败: {e}")
    
    try:
        from 邮箱接收 import main as email_main
        print("✅ 邮箱接收 导入成功")
    except ImportError as e:
        errors.append(f"❌ 邮箱接收 导入失败: {e}")
    
    try:
        from 界面组件 import create_auto_login_frame
        print("✅ 界面组件 导入成功")
    except ImportError as e:
        errors.append(f"❌ 界面组件 导入失败: {e}")
    
    # 测试主程序
    try:
        from 主程序 import create_gui, setup_global_exception_handler
        print("✅ 主程序 导入成功")
    except ImportError as e:
        errors.append(f"❌ 主程序 导入失败: {e}")
    
    # 输出结果
    print("\n" + "="*50)
    if errors:
        print("❌ 发现导入错误:")
        for error in errors:
            print(f"  {error}")
        return False
    else:
        print("✅ 所有模块导入测试通过!")
        return True

if __name__ == "__main__":
    success = test_imports()
    if not success:
        exit(1)
