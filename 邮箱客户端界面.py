import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
from 工具模块 import EMOJI
from 配置管理 import get_config
from 应用管理 import launch_email_client_process

class EmailClientWindow:
    def __init__(self, parent=None):
        self.parent = parent
        self.window = None
        self.email_prefix = ""
        self.email_password = ""
        self.monitoring_process = None
        
    def show(self):
        """显示邮箱客户端窗口"""
        if self.window and self.window.winfo_exists():
            self.window.lift()
            self.window.focus_force()
            return
            
        self.window = tk.Toplevel(self.parent) if self.parent else tk.Tk()
        self.window.title("邮箱验证码监控器")
        self.window.geometry("600x500")
        self.window.resizable(True, True)
        self.window.minsize(500, 400)
        
        # 加载配置
        self.load_config()
        
        # 创建界面
        self.create_widgets()
        
        # 设置窗口关闭事件
        self.window.protocol("WM_DELETE_WINDOW", self.on_closing)
        
    def load_config(self):
        """加载配置信息"""
        try:
            config = get_config()
            if config:
                self.email_prefix = config.get('Email', 'prefix', fallback='').strip()
                self.email_password = config.get('Email', 'password', fallback='').strip()
        except Exception as e:
            self.update_status(f"加载配置失败: {e}")
            
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.window, padding="15")
        main_frame.grid(row=0, column=0, sticky="nsew")
        self.window.columnconfigure(0, weight=1)
        self.window.rowconfigure(0, weight=1)
        
        # 标题
        title_frame = ttk.Frame(main_frame)
        title_frame.grid(row=0, column=0, sticky="ew", pady=(0, 15))
        
        title_label = ttk.Label(title_frame, text=f"{EMOJI['EMAIL']} 邮箱验证码监控器", 
                               font=("Microsoft YaHei", 14, "bold"))
        title_label.pack()
        
        subtitle_label = ttk.Label(title_frame, text="自动监控邮箱并获取验证码", 
                                  font=("Microsoft YaHei", 9))
        subtitle_label.pack()
        
        # 登录信息框架
        login_frame = ttk.LabelFrame(main_frame, text="登录信息", padding="10")
        login_frame.grid(row=1, column=0, sticky="ew", pady=(0, 10))
        
        # 邮箱账号
        ttk.Label(login_frame, text="邮箱账号:").grid(row=0, column=0, sticky="w", pady=2)
        self.email_entry = ttk.Entry(login_frame, width=40, state='readonly')
        self.email_entry.grid(row=0, column=1, sticky="ew", padx=(10, 0))
        
        # 邮箱密码
        ttk.Label(login_frame, text="邮箱密码:").grid(row=1, column=0, sticky="w", pady=2)
        self.password_entry = ttk.Entry(login_frame, width=40, show="*")
        self.password_entry.grid(row=1, column=1, sticky="ew", padx=(10, 0))
        self.password_entry.insert(0, self.email_password)
        
        login_frame.columnconfigure(1, weight=1)
        
        # 控制按钮框架
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=2, column=0, sticky="ew", pady=(0, 10))
        
        self.start_button = ttk.Button(control_frame, text=f"{EMOJI['START']} 开始监控", 
                                      command=self.start_monitoring)
        self.start_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.stop_button = ttk.Button(control_frame, text=f"{EMOJI['STOP']} 停止监控", 
                                     command=self.stop_monitoring, state='disabled')
        self.stop_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.clear_button = ttk.Button(control_frame, text="清空日志", 
                                      command=self.clear_log)
        self.clear_button.pack(side=tk.LEFT)
        
        # 状态指示器
        self.status_label = ttk.Label(control_frame, text="● 就绪", foreground="green")
        self.status_label.pack(side=tk.RIGHT)
        
        # 监控日志
        log_frame = ttk.LabelFrame(main_frame, text="监控日志", padding="5")
        log_frame.grid(row=3, column=0, sticky="nsew", pady=(0, 10))
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, wrap=tk.WORD, 
                                                 state='disabled', font=("Consolas", 9))
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
        # 验证码结果框架
        result_frame = ttk.LabelFrame(main_frame, text="验证码结果", padding="10")
        result_frame.grid(row=4, column=0, sticky="ew")
        
        ttk.Label(result_frame, text="验证码:").grid(row=0, column=0, sticky="w")
        self.code_entry = ttk.Entry(result_frame, width=20, font=("Consolas", 12, "bold"))
        self.code_entry.grid(row=0, column=1, sticky="ew", padx=(10, 10))
        
        self.copy_button = ttk.Button(result_frame, text="复制", command=self.copy_code)
        self.copy_button.grid(row=0, column=2)
        
        result_frame.columnconfigure(1, weight=1)
        
        # 设置权重
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(3, weight=1)
        
        # 更新邮箱显示
        self.update_email_display()
        
    def update_email_display(self):
        """更新邮箱显示"""
        if self.email_prefix:
            monitoring_email = f"{self.email_prefix}@2925.com"
            self.email_entry.config(state='normal')
            self.email_entry.delete(0, tk.END)
            self.email_entry.insert(0, monitoring_email)
            self.email_entry.config(state='readonly')
        else:
            self.update_status("请先在主界面设置邮箱凭据")
            
    def start_monitoring(self):
        """开始监控邮箱"""
        if not self.email_prefix or not self.password_entry.get():
            messagebox.showerror("错误", "请先设置邮箱凭据")
            return
            
        self.start_button.config(state='disabled')
        self.stop_button.config(state='normal')
        self.status_label.config(text="● 监控中", foreground="orange")
        
        # 清空之前的验证码
        self.code_entry.delete(0, tk.END)
        
        def monitoring_task():
            email_to_watch = f"{self.email_prefix}@2925.com"
            password = self.password_entry.get()
            
            def process_output(message):
                """处理监控输出"""
                if "VERIFICATION_CODE:" in message:
                    try:
                        code = message.split("VERIFICATION_CODE:", 1)[1].strip()
                        self.window.after(0, self.on_code_received, code)
                    except IndexError:
                        self.window.after(0, self.update_status, message)
                else:
                    self.window.after(0, self.update_status, message)
            
            try:
                launch_email_client_process(email_to_watch, password, process_output)
            except Exception as e:
                self.window.after(0, self.update_status, f"监控出错: {e}")
                self.window.after(0, self.stop_monitoring)
        
        # 启动监控线程
        self.monitoring_thread = threading.Thread(target=monitoring_task, daemon=True)
        self.monitoring_thread.start()
        
        self.update_status("开始监控邮箱...")
        
    def stop_monitoring(self):
        """停止监控"""
        self.start_button.config(state='normal')
        self.stop_button.config(state='disabled')
        self.status_label.config(text="● 已停止", foreground="red")
        self.update_status("监控已停止")
        
    def on_code_received(self, code):
        """收到验证码时的处理"""
        self.code_entry.delete(0, tk.END)
        self.code_entry.insert(0, code)
        self.status_label.config(text="● 收到验证码", foreground="blue")
        self.update_status(f"成功获取验证码: {code}")
        
        # 自动复制到剪贴板
        try:
            self.window.clipboard_clear()
            self.window.clipboard_append(code)
            self.update_status("验证码已自动复制到剪贴板")
        except:
            pass
            
        # 自动停止监控
        self.stop_monitoring()
        
    def copy_code(self):
        """复制验证码"""
        code = self.code_entry.get()
        if code:
            try:
                self.window.clipboard_clear()
                self.window.clipboard_append(code)
                self.update_status("验证码已复制到剪贴板")
            except Exception as e:
                self.update_status(f"复制失败: {e}")
        else:
            messagebox.showwarning("警告", "没有验证码可复制")
            
    def clear_log(self):
        """清空日志"""
        self.log_text.config(state='normal')
        self.log_text.delete('1.0', tk.END)
        self.log_text.config(state='disabled')
        
    def update_status(self, message):
        """更新状态日志"""
        if not self.window or not self.window.winfo_exists():
            return
            
        self.log_text.config(state='normal')
        self.log_text.insert(tk.END, message + "\n")
        self.log_text.config(state='disabled')
        self.log_text.see(tk.END)
        
    def on_closing(self):
        """窗口关闭事件"""
        if self.stop_button['state'] == 'normal':
            self.stop_monitoring()
        self.window.destroy()

def show_email_client(parent=None):
    """显示邮箱客户端窗口"""
    client = EmailClientWindow(parent)
    client.show()
    return client

if __name__ == "__main__":
    # 独立运行测试
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    client = show_email_client()
    root.mainloop()
