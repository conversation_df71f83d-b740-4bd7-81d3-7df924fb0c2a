import sys
import os
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import traceback
import threading
import logging
import multiprocessing

from 工具模块 import EMOJI
from 配置管理 import get_config, save_config
from 应用管理 import run_full_reset_flow
from 网页操作 import generate_random_email, run_auto_login_flow
from 邮箱客户端界面 import show_email_client
from 界面组件 import (
    create_auto_login_frame,
    create_email_credentials_frame,
    create_account_copy_frame,
    create_system_tools_frame,
    create_browser_settings_frame,
)

# --- Global Exception Handler to catch errors when running with pythonw.exe ---
def setup_global_exception_handler():
    """Sets up a global exception handler to log errors to a file and show a message box."""
    log_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'error.log')
    logging.basicConfig(
        filename=log_file,
        level=logging.ERROR,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

    def handle_exception(exc_type, exc_value, exc_traceback):
        if issubclass(exc_type, KeyboardInterrupt):
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return

        logging.error("Unhandled exception caught:", exc_info=(exc_type, exc_value, exc_traceback))

        try:
            error_root = tk.Tk()
            error_root.withdraw()
            messagebox.showerror(
                "Fatal Error",
                f"A critical error occurred and the application must close.\n\n"
                f"Please check the 'error.log' file for technical details.\n\n"
                f"Error: {exc_value}"
            )
            error_root.destroy()
        except Exception:
            pass
            
    sys.excepthook = handle_exception
# --- End Exception Handler ---

def create_gui():
    """
    Creates and runs the Tkinter GUI.
    """
    root = tk.Tk()
    root.title("续杯工具")
    
    root.withdraw()
    
    try:
        config = get_config()
        if not config:
            messagebox.showerror("致命错误", "无法加载或创建配置文件。应用程序将退出。")
            root.destroy()
            return
            
        email_prefix = config.get('Email', 'prefix', fallback='').strip()
        email_password = config.get('Email', 'password', fallback='').strip()
        browser_path = config.get('Browser', 'chrome_path', fallback='')
        
        root.deiconify()
    except Exception as e:
        messagebox.showerror("初始化错误", f"加载配置时出错: {e}\n\n请检查 'error.log' 文件。")
        root.destroy()
        return

    root.geometry("850x630")
    root.resizable(True, True)
    root.minsize(700, 500)

    mainframe = ttk.Frame(root, padding="12 12 12 12")
    mainframe.grid(row=0, column=0, sticky="nsew")
    root.columnconfigure(0, weight=1)
    root.rowconfigure(0, weight=1)

    mainframe.columnconfigure(0, weight=1) 
    mainframe.columnconfigure(1, weight=2)
    mainframe.rowconfigure(0, weight=1)

    left_frame = ttk.Frame(mainframe)
    left_frame.grid(row=0, column=0, sticky="nsew", padx=(0, 10))
    left_frame.columnconfigure(0, weight=1)

    # --- Widgets ---
    info_label = ttk.Label(left_frame, text="请选择一个操作：", wraplength=400)
    auto_login_frame, auto_login_button = create_auto_login_frame(left_frame)
    credentials_frame, prefix_entry, password_entry, credentials_save_button = create_email_credentials_frame(left_frame, email_prefix, email_password)
    browser_settings_frame, browser_path_entry, browser_browse_button, browser_save_button = create_browser_settings_frame(left_frame, browser_path)
    account_frame, email_entry, email_copy_button = create_account_copy_frame(left_frame)
    system_tools_frame, reset_environment_button, open_email_client_button = create_system_tools_frame(left_frame)

    status_box = scrolledtext.ScrolledText(mainframe, width=50, height=15, wrap=tk.WORD, state='disabled')

    # --- Layout ---
    info_label.grid(row=1, column=0, sticky="we", pady=(0, 10))
    auto_login_frame.grid(row=2, column=0, sticky="we", pady=5)
    credentials_frame.grid(row=3, column=0, sticky="we", pady=10)
    browser_settings_frame.grid(row=4, column=0, sticky="we", pady=10)
    account_frame.grid(row=5, column=0, sticky="we", pady=10)
    system_tools_frame.grid(row=6, column=0, sticky="we", pady=10)
    status_box.grid(row=0, column=1, rowspan=8, sticky="nsew")
    
    # --- Logic ---
    def update_status(message):
        if not root.winfo_exists(): return

        status_box.configure(state='normal')
        status_box.insert(tk.END, message + "\n")
        status_box.configure(state='disabled')
        status_box.see(tk.END)
        root.update_idletasks()
    
    def update_random_email():
        email_entry.config(state='normal')
        email_entry.delete(0, tk.END)

        new_email = generate_random_email(update_status) or ""
        email_entry.insert(0, new_email)
        
        email_entry.config(state='readonly')
        root.after(1000, update_random_email) # Update every 1s

    def clear_status_and_disable_buttons():
        reset_environment_button.configure(state='disabled')
        auto_login_button.configure(state='disabled')
        open_email_client_button.configure(state='disabled')
        status_box.configure(state='normal')
        status_box.delete('1.0', tk.END)
        status_box.configure(state='disabled')

    def re_enable_buttons():
        if not root.winfo_exists(): return
        reset_environment_button.configure(state='normal')
        auto_login_button.configure(state='normal')
        open_email_client_button.configure(state='normal')

    def copy_to_clipboard(text_to_copy, item_name):
        if not root.winfo_exists(): return
        root.clipboard_clear()
        root.clipboard_append(text_to_copy)
        update_status(f"'{item_name}' 已复制到剪贴板。")

    def handle_open_email_client_click():
        """打开独立的邮箱客户端窗口"""
        try:
            show_email_client(root)
            update_status(f"{EMOJI['SUCCESS']} 邮箱客户端已打开")
        except Exception as e:
            update_status(f"{EMOJI['ERROR']} 打开邮箱客户端失败: {e}")

    def handle_reset_environment_click():
        def task():
            root.after(0, clear_status_and_disable_buttons)
            try:
                run_full_reset_flow(lambda msg: root.after(0, update_status, msg))
            except Exception as e:
                root.after(0, lambda e=e: update_status(f"{EMOJI['ERROR']} 环境重置过程中发生未知错误: {e}"))
                root.after(0, lambda: update_status(traceback.format_exc()))
            finally:
                re_enable_buttons()
        
        threading.Thread(target=task, daemon=True).start()

    def handle_auto_login_click():
        def task():
            root.after(0, clear_status_and_disable_buttons)
            try:
                password = password_entry.get()
                # 固定的监控邮箱
                monitoring_email = f"{email_prefix}@2925.com"
                # 界面上显示的随机邮箱用于登录
                login_email = email_entry.get()
                
                run_auto_login_flow(monitoring_email, login_email, password, lambda msg: root.after(0, update_status, msg))
            except Exception as e:
                root.after(0, lambda e=e: update_status(f"错误: 一键登录过程中发生未知错误: {e}"))
                root.after(0, lambda: update_status(traceback.format_exc()))
            finally:
                root.after(0, re_enable_buttons)

        threading.Thread(target=task, daemon=True).start()

    def handle_save_credentials_click():
        nonlocal email_prefix, email_password
        new_prefix = prefix_entry.get().strip()
        new_password = password_entry.get() # Don't strip password
        
        config.set('Email', 'prefix', new_prefix)
        config.set('Email', 'password', new_password)

        if save_config(config):
            email_prefix = new_prefix
            email_password = new_password
            update_status(f"{EMOJI['SUCCESS']} 凭据已保存。")

            # Refresh the random email display
            email_entry.config(state='normal')
            email_entry.delete(0, tk.END)
            updated_email = generate_random_email(update_status) or ""
            email_entry.insert(0, updated_email)
            email_entry.config(state='readonly')
        else:
            update_status(f"错误: 保存凭据失败。")

    def handle_browse_browser_path():
        from tkinter import filedialog
        # 在Windows上，只显示exe文件
        filetypes = (('Executable files', '*.exe'), ('All files', '*.*')) if sys.platform == "win32" else None
        filepath = filedialog.askopenfilename(title="选择Chrome浏览器可执行文件", filetypes=filetypes)
        if filepath:
            browser_path_entry.delete(0, tk.END)
            browser_path_entry.insert(0, filepath)
            update_status(f"浏览器路径已更新为: {filepath}")

    def handle_save_browser_path():
        nonlocal browser_path
        new_path = browser_path_entry.get().strip()
        if not new_path or not os.path.exists(new_path):
            update_status(f"错误: 路径无效或文件不存在: {new_path}")
            return
            
        config.set('Browser', 'chrome_path', new_path)

        if save_config(config):
            browser_path = new_path
            update_status(f"{EMOJI['SUCCESS']} 浏览器路径已保存。")
        else:
            update_status(f"错误: 保存浏览器路径失败。")

    email_copy_button.configure(command=lambda: copy_to_clipboard(email_entry.get(), "邮箱"))
    reset_environment_button.configure(command=handle_reset_environment_click)
    auto_login_button.configure(command=handle_auto_login_click)
    credentials_save_button.configure(command=handle_save_credentials_click)
    open_email_client_button.configure(command=handle_open_email_client_click)
    browser_browse_button.configure(command=handle_browse_browser_path)
    browser_save_button.configure(command=handle_save_browser_path)
    
    update_status("准备就绪。请点击按钮开始。")
    update_random_email()

    root.mainloop()

if __name__ == "__main__":
    # For multiprocessing to work correctly when packaged
    multiprocessing.freeze_support()

    # 确保在无终端环境下也能正常运行
    try:
        # 重定向标准输出和错误输出到日志文件（仅在打包后的exe中）
        if getattr(sys, 'frozen', False):
            # 运行在PyInstaller打包的环境中
            log_dir = os.path.dirname(os.path.abspath(sys.executable))
            stdout_log = os.path.join(log_dir, 'stdout.log')
            stderr_log = os.path.join(log_dir, 'stderr.log')

            try:
                sys.stdout = open(stdout_log, 'w', encoding='utf-8')
                sys.stderr = open(stderr_log, 'w', encoding='utf-8')
            except:
                pass  # 如果无法创建日志文件，继续运行
    except:
        pass  # 静默处理任何错误

    setup_global_exception_handler()
    create_gui()