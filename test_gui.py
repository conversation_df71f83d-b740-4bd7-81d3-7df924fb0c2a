#!/usr/bin/env python3
"""
简单的GUI测试脚本，用于验证打包后的应用是否能正常运行
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

def test_gui():
    """测试GUI是否能正常启动"""
    try:
        root = tk.Tk()
        root.title("GUI测试")
        root.geometry("300x200")
        
        # 隐藏窗口，然后显示消息框
        root.withdraw()
        
        result = messagebox.askyesno(
            "GUI测试", 
            "GUI应用启动成功！\n\n这表明打包后的应用可以正常运行而不依赖终端。\n\n是否继续启动主程序？"
        )
        
        root.destroy()
        
        if result:
            # 启动主程序
            try:
                from 主程序 import create_gui, setup_global_exception_handler
                setup_global_exception_handler()
                create_gui()
            except Exception as e:
                messagebox.showerror("错误", f"启动主程序时出错: {e}")
        
    except Exception as e:
        # 如果GUI失败，尝试写入日志文件
        try:
            with open('gui_test_error.log', 'w', encoding='utf-8') as f:
                f.write(f"GUI测试失败: {e}\n")
                f.write(f"Python版本: {sys.version}\n")
                f.write(f"当前目录: {os.getcwd()}\n")
        except:
            pass

if __name__ == "__main__":
    test_gui()
