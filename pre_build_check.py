#!/usr/bin/env python3
"""
打包前检查脚本 - 确保所有依赖和文件都准备就绪
"""

import os
import sys
import subprocess
import importlib.util

def check_file_exists(filepath, description):
    """检查文件是否存在"""
    if os.path.exists(filepath):
        print(f"✅ {description}: {filepath}")
        return True
    else:
        print(f"❌ {description} 不存在: {filepath}")
        return False

def check_module_import(module_name):
    """检查模块是否可以导入"""
    try:
        spec = importlib.util.find_spec(module_name)
        if spec is not None:
            print(f"✅ 模块 {module_name} 可用")
            return True
        else:
            print(f"❌ 模块 {module_name} 不可用")
            return False
    except ImportError:
        print(f"❌ 模块 {module_name} 导入失败")
        return False

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    if version.major == 3 and version.minor >= 8:
        print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
        return True
    else:
        print(f"❌ Python版本过低: {version.major}.{version.minor}.{version.micro} (需要3.8+)")
        return False

def main():
    """主检查函数"""
    print("🔍 开始打包前检查...\n")
    
    all_checks_passed = True
    
    # 检查Python版本
    print("1. 检查Python版本")
    if not check_python_version():
        all_checks_passed = False
    print()
    
    # 检查必要文件
    print("2. 检查必要文件")
    required_files = [
        ("主程序.py", "主程序文件"),
        ("build.py", "打包脚本"),
        ("requirements.txt", "依赖文件"),
        ("drivers/chromedriver.exe", "Chrome驱动"),
        ("工具模块.py", "工具模块"),
        ("配置管理.py", "配置管理模块"),
        ("应用管理.py", "应用管理模块"),
        ("网页操作.py", "网页操作模块"),
        ("邮箱接收.py", "邮箱接收模块"),
        ("界面组件.py", "界面组件模块")
    ]
    
    for filepath, description in required_files:
        if not check_file_exists(filepath, description):
            all_checks_passed = False
    print()
    
    # 检查必要的Python模块
    print("3. 检查必要的Python模块")
    required_modules = [
        "tkinter",
        "PyInstaller",
        "colorama",
        "pywinauto", 
        "DrissionPage",
        "pyperclip",
        "bs4",
        "sqlite3",
        "configparser"
    ]
    
    for module in required_modules:
        if not check_module_import(module):
            all_checks_passed = False
    print()
    
    # 检查项目模块语法
    print("4. 检查项目模块语法")
    project_files = [
        "主程序.py",
        "build.py", 
        "工具模块.py",
        "配置管理.py",
        "应用管理.py",
        "网页操作.py",
        "邮箱接收.py",
        "界面组件.py"
    ]
    
    for file in project_files:
        try:
            result = subprocess.run([sys.executable, "-m", "py_compile", file], 
                                  capture_output=True, text=True, check=True)
            print(f"✅ {file} 语法检查通过")
        except subprocess.CalledProcessError as e:
            print(f"❌ {file} 语法错误: {e.stderr}")
            all_checks_passed = False
    print()
    
    # 最终结果
    print("="*50)
    if all_checks_passed:
        print("🎉 所有检查通过！可以开始打包。")
        print("\n运行以下命令开始打包:")
        print("python build.py")
        return True
    else:
        print("❌ 检查失败！请修复上述问题后再打包。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
